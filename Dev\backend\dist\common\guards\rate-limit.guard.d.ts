import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Redis } from 'ioredis';
export declare const RateLimit: (limit: number, windowMs?: number) => (target: any, propertyName: string, descriptor: PropertyDescriptor) => void;
export declare class RateLimitGuard implements CanActivate {
    private reflector;
    private readonly redis;
    constructor(reflector: Reflector, redis: Redis);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private generateKey;
}
