"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ai_module_1 = require("../ai/ai.module");
const redis_module_1 = require("../redis/redis.module");
const translation_controller_1 = require("./translation.controller");
const translation_service_1 = require("./translation.service");
const translation_entity_1 = require("./entities/translation.entity");
const user_entity_1 = require("../auth/entities/user.entity");
const cache_service_1 = require("../../common/services/cache.service");
const translation_cache_service_1 = require("./services/translation-cache.service");
let TranslationModule = class TranslationModule {
};
exports.TranslationModule = TranslationModule;
exports.TranslationModule = TranslationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([translation_entity_1.Translation, user_entity_1.User]),
            ai_module_1.AiModule,
            redis_module_1.RedisModule,
        ],
        controllers: [translation_controller_1.TranslationController],
        providers: [
            translation_service_1.TranslationService,
            cache_service_1.CacheService,
            translation_cache_service_1.TranslationCacheService,
        ],
        exports: [translation_service_1.TranslationService, translation_cache_service_1.TranslationCacheService],
    })
], TranslationModule);
//# sourceMappingURL=translation.module.js.map