import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { DataSource } from 'typeorm';
export declare class QueryOptimizationInterceptor implements NestInterceptor {
    private readonly dataSource;
    private readonly logger;
    constructor(dataSource: DataSource);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
