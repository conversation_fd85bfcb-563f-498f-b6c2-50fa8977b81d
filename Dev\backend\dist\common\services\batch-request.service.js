"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var BatchRequestService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationBatchService = exports.BatchRequestService = void 0;
const common_1 = require("@nestjs/common");
let BatchRequestService = BatchRequestService_1 = class BatchRequestService {
    logger = new common_1.Logger(BatchRequestService_1.name);
    batches = new Map();
    registerBatch(batchKey, config) {
        this.batches.set(batchKey, {
            requests: [],
            timer: null,
            config,
        });
    }
    async addToBatch(batchKey, requestId, data) {
        const batch = this.batches.get(batchKey);
        if (!batch) {
            throw new Error(`Batch key "${batchKey}" not found`);
        }
        return new Promise((resolve, reject) => {
            const request = {
                id: requestId,
                data,
                resolve,
                reject,
                timestamp: Date.now(),
            };
            batch.requests.push(request);
            if (batch.requests.length >= batch.config.maxBatchSize) {
                this.processBatch(batchKey);
            }
            else if (!batch.timer) {
                batch.timer = setTimeout(() => {
                    this.processBatch(batchKey);
                }, batch.config.maxWaitTime);
            }
        });
    }
    async processBatch(batchKey) {
        const batch = this.batches.get(batchKey);
        if (!batch || batch.requests.length === 0) {
            return;
        }
        const requests = [...batch.requests];
        batch.requests = [];
        if (batch.timer) {
            clearTimeout(batch.timer);
            batch.timer = null;
        }
        try {
            const startTime = Date.now();
            const results = await batch.config.processor(requests);
            const processingTime = Date.now() - startTime;
            this.logger.debug(`Processed batch "${batchKey}": ${requests.length} requests in ${processingTime}ms`);
            requests.forEach((request, index) => {
                if (results[index] !== undefined) {
                    request.resolve(results[index]);
                }
                else {
                    request.reject(new Error('No result for request'));
                }
            });
        }
        catch (error) {
            this.logger.error(`Batch processing failed for "${batchKey}":`, error);
            requests.forEach(request => {
                request.reject(error);
            });
        }
    }
    getBatchStats() {
        const stats = {};
        for (const [key, batch] of this.batches.entries()) {
            stats[key] = {
                pendingRequests: batch.requests.length,
                hasTimer: batch.timer !== null,
                maxBatchSize: batch.config.maxBatchSize,
                maxWaitTime: batch.config.maxWaitTime,
            };
        }
        return stats;
    }
    cleanupExpiredRequests() {
        const now = Date.now();
        const maxAge = 30000;
        for (const [key, batch] of this.batches.entries()) {
            const expiredRequests = batch.requests.filter(request => now - request.timestamp > maxAge);
            if (expiredRequests.length > 0) {
                this.logger.warn(`Found ${expiredRequests.length} expired requests in batch "${key}"`);
                batch.requests = batch.requests.filter(request => now - request.timestamp <= maxAge);
                expiredRequests.forEach(request => {
                    request.reject(new Error('Request timeout'));
                });
            }
        }
    }
};
exports.BatchRequestService = BatchRequestService;
exports.BatchRequestService = BatchRequestService = BatchRequestService_1 = __decorate([
    (0, common_1.Injectable)()
], BatchRequestService);
class TranslationBatchService {
    batchService;
    constructor(batchService) {
        this.batchService = batchService;
        this.batchService.registerBatch('translation', {
            maxBatchSize: 5,
            maxWaitTime: 100,
            processor: this.processTranslationBatch.bind(this),
        });
    }
    async batchTranslate(text, sourceLang, targetLang) {
        const requestId = `${sourceLang}-${targetLang}-${Date.now()}`;
        return this.batchService.addToBatch('translation', requestId, {
            text,
            sourceLang,
            targetLang,
        });
    }
    async processTranslationBatch(requests) {
        return requests.map(req => `Translated: ${req.data.text}`);
    }
}
exports.TranslationBatchService = TranslationBatchService;
//# sourceMappingURL=batch-request.service.js.map