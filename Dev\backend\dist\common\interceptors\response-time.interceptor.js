"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ResponseTimeInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseTimeInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const api_optimization_service_1 = require("../services/api-optimization.service");
let ResponseTimeInterceptor = ResponseTimeInterceptor_1 = class ResponseTimeInterceptor {
    apiOptimizationService;
    logger = new common_1.Logger(ResponseTimeInterceptor_1.name);
    constructor(apiOptimizationService) {
        this.apiOptimizationService = apiOptimizationService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const startTime = Date.now();
        request.startTime = startTime;
        return next.handle().pipe((0, operators_1.tap)(() => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            response.set('X-Response-Time', `${responseTime}ms`);
            const isCacheHit = response.getHeader('X-Cache') === 'HIT';
            if (this.apiOptimizationService) {
                this.apiOptimizationService.recordRequest(request.route?.path || request.originalUrl, request.method, responseTime, false, isCacheHit);
            }
            if (responseTime > 1000) {
                this.logger.warn(`Slow request detected: ${request.method} ${request.originalUrl} - ${responseTime}ms`, {
                    method: request.method,
                    url: request.originalUrl,
                    userAgent: request.get('User-Agent'),
                    responseTime,
                    cacheHit: isCacheHit,
                    statusCode: response.statusCode,
                });
            }
            if (process.env.NODE_ENV === 'development') {
                this.logger.debug(`${request.method} ${request.originalUrl} - ${responseTime}ms ${isCacheHit ? '[CACHED]' : ''}`);
            }
        }), (0, operators_1.catchError)((error) => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            if (this.apiOptimizationService) {
                this.apiOptimizationService.recordRequest(request.route?.path || request.originalUrl, request.method, responseTime, true, false);
            }
            this.logger.error(`Error request: ${request.method} ${request.originalUrl} - ${responseTime}ms`, {
                method: request.method,
                url: request.originalUrl,
                responseTime,
                error: error.message,
                statusCode: error.status || 500,
            });
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
};
exports.ResponseTimeInterceptor = ResponseTimeInterceptor;
exports.ResponseTimeInterceptor = ResponseTimeInterceptor = ResponseTimeInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Optional)()),
    __param(0, (0, common_1.Inject)(api_optimization_service_1.ApiOptimizationService)),
    __metadata("design:paramtypes", [api_optimization_service_1.ApiOptimizationService])
], ResponseTimeInterceptor);
//# sourceMappingURL=response-time.interceptor.js.map