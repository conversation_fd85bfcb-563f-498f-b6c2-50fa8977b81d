"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPerformanceIndexes1735900000000 = void 0;
class AddPerformanceIndexes1735900000000 {
    name = 'AddPerformanceIndexes1735900000000';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_translations_userId_createdAt" 
      ON "translations" ("userId", "createdAt" DESC)
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_translations_userId_sourceLang_targetLang" 
      ON "translations" ("userId", "sourceLang", "targetLang")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_translations_userId_isFavorite" 
      ON "translations" ("userId", "isFavorite")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_translations_status" 
      ON "translations" ("status")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_translations_cached" 
      ON "translations" ("cached")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_users_role" 
      ON "users" ("role")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_users_status" 
      ON "users" ("status")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_users_createdAt" 
      ON "users" ("createdAt")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_users_active_lastLoginAt" 
      ON "users" ("lastLoginAt") 
      WHERE "status" = 'active'
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_translations_userId_status_createdAt" 
      ON "translations" ("userId", "status", "createdAt" DESC)
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_translations_userId_createdAt"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_translations_userId_sourceLang_targetLang"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_translations_userId_isFavorite"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_translations_status"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_translations_cached"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_role"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_status"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_createdAt"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_active_lastLoginAt"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_translations_userId_status_createdAt"`);
    }
}
exports.AddPerformanceIndexes1735900000000 = AddPerformanceIndexes1735900000000;
//# sourceMappingURL=1735900000000-AddPerformanceIndexes.js.map