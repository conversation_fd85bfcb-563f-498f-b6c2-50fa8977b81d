import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import compression from 'compression';

@Injectable()
export class CompressionMiddleware implements NestMiddleware {
  private compression = compression({
    // 启用 gzip 压缩
    threshold: 1024, // 只压缩大于 1KB 的响应
    level: 6, // 压缩级别 (1-9, 6是平衡点)
    memLevel: 8, // 内存级别 (1-9)
    
    // 过滤器 - 决定是否压缩
    filter: (req: Request, res: Response) => {
      // 不压缩已经压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }

      // 检查内容类型
      const contentType = res.getHeader('content-type') as string;
      if (contentType) {
        // 压缩文本内容
        return /text|json|javascript|css|xml|svg/.test(contentType);
      }

      // 默认使用 compression 的判断
      return compression.filter(req, res);
    },

    // 压缩策略配置
    strategy: 0, // Z_DEFAULT_STRATEGY
    windowBits: 15,
    chunkSize: 16384, // 16KB chunks
  });

  use(req: Request, res: Response, next: NextFunction) {
    // 添加压缩相关的头信息
    res.setHeader('Vary', 'Accept-Encoding');
    
    // 对于 API 响应，设置缓存控制
    if (req.path.startsWith('/api/')) {
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
    }

    this.compression(req, res, next);
  }
}