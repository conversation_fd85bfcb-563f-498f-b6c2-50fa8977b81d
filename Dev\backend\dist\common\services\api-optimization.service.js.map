{"version": 3, "file": "api-optimization.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/api-optimization.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AAqB7C,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAChB,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IACjD,OAAO,GAAG,IAAI,GAAG,EAAyB,CAAC;IAC3C,aAAa,GAAG,OAAO,CAAC;IAEzC;QAEE,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7D,CAAC;IAKD,aAAa,CACX,QAAgB,EAChB,MAAc,EACd,YAAoB,EACpB,UAAmB,KAAK,EACxB,aAAsB,KAAK;QAE3B,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;YACrC,SAAS,EAAE,CAAC;YACZ,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,CAAC;YACT,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,KAAK,CAAC,SAAS,IAAI,YAAY,CAAC;QAChC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;QAEjB,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAG7B,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,GAAG,MAAM,YAAY,IAAI,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAKD,UAAU;QACR,MAAM,MAAM,GAAiB,EAAE,CAAC;QAEhC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAE7C,MAAM,CAAC,IAAI,CAAC;gBACV,QAAQ;gBACR,MAAM;gBACN,aAAa,EAAE,KAAK,CAAC,KAAK;gBAC1B,mBAAmB,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxE,SAAS,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnE,YAAY,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC;oBACrD,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG;oBACjE,CAAC,CAAC,CAAC;gBACL,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa;aAC/C,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;IAClE,CAAC;IAKD,uBAAuB;QAKrB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAElC,OAAO;YACL,aAAa,EAAE,OAAO;iBACnB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,GAAG,IAAI,IAAI,CAAC,CAAC,aAAa,GAAG,EAAE,CAAC;iBACjE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,CAAC,mBAAmB,CAAC;iBAC7D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YAEf,kBAAkB,EAAE,OAAO;iBACxB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,GAAG,EAAE,CAAC;iBACpD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;iBACzC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YAEf,iBAAiB,EAAE,OAAO;iBACvB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,EAAE,IAAI,CAAC,CAAC,aAAa,GAAG,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC;iBAC9E,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,CAAC;iBAC/C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;SAChB,CAAC;IACJ,CAAC;IAKD,0BAA0B;QACxB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEnD,IAAI,WAAW,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,WAAW,CAAC,IAAI,CACd,MAAM,WAAW,CAAC,aAAa,CAAC,MAAM,wBAAwB,CAC/D,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,WAAW,CAAC,IAAI,CACd,MAAM,WAAW,CAAC,kBAAkB,CAAC,MAAM,wBAAwB,CACpE,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,WAAW,CAAC,IAAI,CACd,MAAM,WAAW,CAAC,iBAAiB,CAAC,MAAM,yBAAyB,CACpE,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACpD,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEhD,IAAI,aAAa,GAAG,KAAK,EAAE,CAAC;YAC1B,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKO,YAAY;QAClB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACpD,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEhD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,aAAa,MAAM,CAAC,CAAC;YAGxD,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnD,IAAI,WAAW,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;gBACpC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAKD,oBAAoB;QAgBlB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAC3E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;QACjG,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/F,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;QAC7D,MAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,YAAY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1G,OAAO;YACL,OAAO,EAAE;gBACP,cAAc,EAAE,OAAO,CAAC,MAAM;gBAC9B,aAAa;gBACb,mBAAmB,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBACtE,gBAAgB,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7E,mBAAmB,EAAE,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aAC9F;YACD,OAAO;YACP,WAAW,EAAE,IAAI,CAAC,uBAAuB,EAAE;YAC3C,WAAW,EAAE,IAAI,CAAC,0BAA0B,EAAE;SAC/C,CAAC;IACJ,CAAC;CACF,CAAA;AAxMY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;;GACA,sBAAsB,CAwMlC"}