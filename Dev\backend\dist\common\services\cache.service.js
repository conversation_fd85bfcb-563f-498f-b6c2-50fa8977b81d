"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ioredis_1 = __importDefault(require("ioredis"));
const redis_module_1 = require("../../modules/redis/redis.module");
let CacheService = class CacheService {
    redis;
    configService;
    defaultTTL;
    defaultNamespace;
    constructor(redis, configService) {
        this.redis = redis;
        this.configService = configService;
        this.defaultTTL = this.configService.get('cache.defaultTTL', 3600);
        this.defaultNamespace = this.configService.get('cache.defaultNamespace', 'syntext');
    }
    generateKey(key, namespace) {
        const ns = namespace || this.defaultNamespace;
        return `${ns}:${key}`;
    }
    async set(key, value, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        const ttl = options?.ttl || this.defaultTTL;
        try {
            const serializedValue = JSON.stringify(value);
            await this.redis.setex(cacheKey, ttl, serializedValue);
        }
        catch (error) {
            console.error(`Failed to set cache for key ${cacheKey}:`, error);
        }
    }
    async get(key, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            const value = await this.redis.get(cacheKey);
            if (value === null) {
                return null;
            }
            return JSON.parse(value);
        }
        catch (error) {
            console.error(`Failed to get cache for key ${cacheKey}:`, error);
            return null;
        }
    }
    async del(key, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            await this.redis.del(cacheKey);
        }
        catch (error) {
            console.error(`Failed to delete cache for key ${cacheKey}:`, error);
        }
    }
    async delPattern(pattern, options) {
        const searchPattern = this.generateKey(pattern, options?.namespace);
        try {
            const keys = await this.redis.keys(searchPattern);
            if (keys.length > 0) {
                await this.redis.del(...keys);
            }
        }
        catch (error) {
            console.error(`Failed to delete cache pattern ${searchPattern}:`, error);
        }
    }
    async exists(key, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            const result = await this.redis.exists(cacheKey);
            return result === 1;
        }
        catch (error) {
            console.error(`Failed to check cache existence for key ${cacheKey}:`, error);
            return false;
        }
    }
    async expire(key, ttl, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            await this.redis.expire(cacheKey, ttl);
        }
        catch (error) {
            console.error(`Failed to set expiration for key ${cacheKey}:`, error);
        }
    }
    async mget(keys, options) {
        const cacheKeys = keys.map(key => this.generateKey(key, options?.namespace));
        try {
            const values = await this.redis.mget(...cacheKeys);
            return values.map(value => {
                if (value === null)
                    return null;
                try {
                    return JSON.parse(value);
                }
                catch {
                    return null;
                }
            });
        }
        catch (error) {
            console.error(`Failed to get multiple cache keys:`, error);
            return keys.map(() => null);
        }
    }
    async mset(keyValuePairs, options) {
        const pipeline = this.redis.pipeline();
        try {
            for (const pair of keyValuePairs) {
                const cacheKey = this.generateKey(pair.key, options?.namespace);
                const serializedValue = JSON.stringify(pair.value);
                const ttl = pair.ttl || this.defaultTTL;
                pipeline.setex(cacheKey, ttl, serializedValue);
            }
            await pipeline.exec();
        }
        catch (error) {
            console.error(`Failed to set multiple cache keys:`, error);
        }
    }
    async incr(key, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            return await this.redis.incr(cacheKey);
        }
        catch (error) {
            console.error(`Failed to increment key ${cacheKey}:`, error);
            return 0;
        }
    }
    async incrby(key, increment, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            return await this.redis.incrby(cacheKey, increment);
        }
        catch (error) {
            console.error(`Failed to increment key ${cacheKey} by ${increment}:`, error);
            return 0;
        }
    }
    async ttl(key, options) {
        const cacheKey = this.generateKey(key, options?.namespace);
        try {
            return await this.redis.ttl(cacheKey);
        }
        catch (error) {
            console.error(`Failed to get TTL for key ${cacheKey}:`, error);
            return -1;
        }
    }
    async flush(namespace) {
        const pattern = this.generateKey('*', namespace);
        await this.delPattern(pattern);
    }
    isConnected() {
        return this.redis.status === 'ready';
    }
    async getInfo() {
        try {
            const info = await this.redis.info();
            return info;
        }
        catch (error) {
            console.error('Failed to get Redis info:', error);
            return null;
        }
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(redis_module_1.REDIS_CLIENT)),
    __metadata("design:paramtypes", [ioredis_1.default,
        config_1.ConfigService])
], CacheService);
//# sourceMappingURL=cache.service.js.map