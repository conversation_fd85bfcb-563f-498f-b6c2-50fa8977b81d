"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var QueryOptimizationInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryOptimizationInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const typeorm_1 = require("typeorm");
let QueryOptimizationInterceptor = QueryOptimizationInterceptor_1 = class QueryOptimizationInterceptor {
    dataSource;
    logger = new common_1.Logger(QueryOptimizationInterceptor_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const startTime = Date.now();
        const connectionPool = this.dataSource.driver.pool;
        if (connectionPool) {
            const activeConnections = connectionPool.totalCount;
            const freeConnections = connectionPool.freeCount;
            if (activeConnections && freeConnections !== undefined) {
                const utilizationRate = ((activeConnections - freeConnections) / activeConnections) * 100;
                request.dbPoolInfo = {
                    active: activeConnections,
                    free: freeConnections,
                    utilization: utilizationRate.toFixed(2) + '%',
                };
                if (utilizationRate > 80) {
                    this.logger.warn(`High database connection pool utilization: ${utilizationRate.toFixed(2)}%`, {
                        active: activeConnections,
                        free: freeConnections,
                        url: request.originalUrl,
                    });
                }
            }
        }
        return next.handle().pipe((0, operators_1.tap)(() => {
            const queryTime = Date.now() - startTime;
            if (queryTime > 500) {
                this.logger.warn(`Slow query detected: ${request.method} ${request.originalUrl} - ${queryTime}ms`, {
                    method: request.method,
                    url: request.originalUrl,
                    queryTime,
                    dbPoolInfo: request.dbPoolInfo,
                });
            }
            if (process.env.NODE_ENV === 'development' && request.dbPoolInfo) {
                this.logger.debug(`DB Pool: ${request.dbPoolInfo.utilization} utilization, Query: ${queryTime}ms`);
            }
        }));
    }
};
exports.QueryOptimizationInterceptor = QueryOptimizationInterceptor;
exports.QueryOptimizationInterceptor = QueryOptimizationInterceptor = QueryOptimizationInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], QueryOptimizationInterceptor);
//# sourceMappingURL=query-optimization.interceptor.js.map