"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ai_service_1 = require("../ai/ai.service");
const translation_entity_1 = require("./entities/translation.entity");
const user_entity_1 = require("../auth/entities/user.entity");
const translation_cache_service_1 = require("./services/translation-cache.service");
let TranslationService = class TranslationService {
    translationRepository;
    userRepository;
    aiService;
    dataSource;
    cacheService;
    constructor(translationRepository, userRepository, aiService, dataSource, cacheService) {
        this.translationRepository = translationRepository;
        this.userRepository = userRepository;
        this.aiService = aiService;
        this.dataSource = dataSource;
        this.cacheService = cacheService;
    }
    async translate(translateDto, userId) {
        const { text, sourceLang, targetLang, context } = translateDto;
        const cachedResult = await this.cacheService.getCachedTranslation(text, sourceLang, targetLang);
        if (cachedResult) {
            return await this.dataSource.transaction(async (manager) => {
                const translation = manager.create(translation_entity_1.Translation, {
                    userId,
                    sourceText: text,
                    translatedText: cachedResult.translatedText,
                    sourceLang: sourceLang,
                    targetLang: targetLang,
                    duration: 50,
                    cached: true,
                    confidence: cachedResult.confidence || 0.95,
                });
                const savedTranslation = await manager.save(translation_entity_1.Translation, translation);
                return {
                    ...cachedResult,
                    id: savedTranslation.id,
                    timestamp: savedTranslation.createdAt,
                };
            });
        }
        return await this.dataSource.transaction(async (manager) => {
            const result = await this.aiService.translateText({
                text,
                sourceLanguage: sourceLang,
                targetLanguage: targetLang,
                priority: 'balanced',
            });
            const translation = manager.create(translation_entity_1.Translation, {
                userId,
                sourceText: text,
                translatedText: result.translatedText,
                sourceLang: sourceLang,
                targetLang: targetLang,
                duration: result.latency,
                cached: false,
                confidence: 0.95,
            });
            const savedTranslation = await manager.save(translation_entity_1.Translation, translation);
            const translationResult = {
                id: savedTranslation.id,
                originalText: text,
                translatedText: result.translatedText,
                sourceLanguage: sourceLang,
                targetLanguage: targetLang,
                confidence: 0.95,
                latency: result.latency,
                timestamp: savedTranslation.createdAt,
            };
            await this.cacheService.cacheTranslation(text, sourceLang, targetLang, translationResult);
            return translationResult;
        });
    }
    async getHistory(userId, query) {
        const { page = 1, limit = 20, sourceLang, targetLang, favorites } = query;
        const cachedHistory = await this.cacheService.getCachedUserHistory(userId, page, limit, { sourceLang, targetLang, favorites });
        if (cachedHistory) {
            return cachedHistory;
        }
        const queryBuilder = this.translationRepository
            .createQueryBuilder('translation')
            .select([
            'translation.id',
            'translation.sourceText',
            'translation.translatedText',
            'translation.sourceLang',
            'translation.targetLang',
            'translation.confidence',
            'translation.duration',
            'translation.isFavorite',
            'translation.createdAt'
        ])
            .where('translation.userId = :userId', { userId })
            .orderBy('translation.createdAt', 'DESC');
        if (sourceLang) {
            queryBuilder.andWhere('translation.sourceLang = :sourceLang', { sourceLang });
        }
        if (targetLang) {
            queryBuilder.andWhere('translation.targetLang = :targetLang', { targetLang });
        }
        if (favorites) {
            queryBuilder.andWhere('translation.isFavorite = :isFavorite', { isFavorite: true });
        }
        const [translations, total] = await queryBuilder
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();
        const translationResults = translations.map(translation => ({
            id: translation.id,
            originalText: translation.sourceText,
            translatedText: translation.translatedText,
            sourceLanguage: translation.sourceLang,
            targetLanguage: translation.targetLang,
            confidence: translation.confidence || 0.95,
            latency: translation.duration,
            timestamp: translation.createdAt,
            isFavorite: translation.isFavorite || false,
        }));
        const result = {
            translations: translationResults,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
        await this.cacheService.cacheUserHistory(userId, page, limit, { sourceLang, targetLang, favorites }, result);
        return result;
    }
    async getStats(userId) {
        const cachedStats = await this.cacheService.getCachedUserStats(userId);
        if (cachedStats) {
            return cachedStats;
        }
        const [stats, characterStats, languagePairStats, recentActivity] = await Promise.all([
            this.translationRepository
                .createQueryBuilder('translation')
                .select([
                'COUNT(*) as totalTranslations',
                'AVG(translation.duration) as avgLatency',
                'SUM(CASE WHEN translation.cached = true THEN 1 ELSE 0 END) as cachedTranslations',
            ])
                .where('translation.userId = :userId', { userId })
                .getRawOne(),
            this.translationRepository
                .createQueryBuilder('translation')
                .select('SUM(LENGTH(translation.sourceText) + LENGTH(translation.translatedText))', 'totalChars')
                .where('translation.userId = :userId', { userId })
                .getRawOne(),
            this.translationRepository
                .createQueryBuilder('translation')
                .select('translation.sourceLang', 'sourceLang')
                .addSelect('translation.targetLang', 'targetLang')
                .addSelect('COUNT(*)', 'count')
                .where('translation.userId = :userId', { userId })
                .groupBy('translation.sourceLang, translation.targetLang')
                .orderBy('count', 'DESC')
                .limit(10)
                .getRawMany(),
            (() => {
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return this.translationRepository
                    .createQueryBuilder('translation')
                    .select('DATE(translation.createdAt)', 'date')
                    .addSelect('COUNT(*)', 'count')
                    .where('translation.userId = :userId', { userId })
                    .andWhere('translation.createdAt >= :sevenDaysAgo', { sevenDaysAgo })
                    .groupBy('DATE(translation.createdAt)')
                    .orderBy('date', 'ASC')
                    .getRawMany();
            })()
        ]);
        const result = {
            totalTranslations: parseInt(stats.totalTranslations),
            avgLatency: parseFloat(stats.avgLatency) || 0,
            cachedTranslations: parseInt(stats.cachedTranslations),
            cacheHitRate: stats.totalTranslations > 0
                ? parseFloat((stats.cachedTranslations / stats.totalTranslations * 100).toFixed(2))
                : 0,
            totalCharacters: parseInt(characterStats?.totalChars) || 0,
            languagePairs: languagePairStats.map(pair => ({
                sourceLanguage: pair.sourceLang,
                targetLanguage: pair.targetLang,
                count: parseInt(pair.count)
            })),
            recentActivity: recentActivity.map(activity => ({
                date: activity.date,
                count: parseInt(activity.count)
            })),
        };
        await this.cacheService.cacheUserStats(userId, result);
        return result;
    }
    async toggleFavorite(translationId, userId) {
        const translation = await this.translationRepository.findOne({
            where: { id: translationId, userId },
        });
        if (!translation) {
            throw new Error('翻译记录不存在或无权限访问');
        }
        translation.isFavorite = !translation.isFavorite;
        await this.translationRepository.save(translation);
        await this.cacheService.clearUserCache(userId);
        return {
            success: true,
            message: translation.isFavorite ? '已添加到收藏' : '已取消收藏',
        };
    }
    async deleteTranslation(translationId, userId) {
        const translation = await this.translationRepository.findOne({
            where: { id: translationId, userId },
        });
        if (!translation) {
            throw new Error('翻译记录不存在或无权限访问');
        }
        await this.translationRepository.remove(translation);
        await this.cacheService.clearUserCache(userId);
        return {
            success: true,
            message: '翻译记录已删除',
        };
    }
};
exports.TranslationService = TranslationService;
exports.TranslationService = TranslationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(translation_entity_1.Translation)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        ai_service_1.AiService,
        typeorm_2.DataSource,
        translation_cache_service_1.TranslationCacheService])
], TranslationService);
//# sourceMappingURL=translation.service.js.map