"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CachingInterceptor = exports.CacheResponse = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const core_1 = require("@nestjs/core");
const redis_module_1 = require("../../modules/redis/redis.module");
const ioredis_1 = require("ioredis");
const CacheResponse = (ttl = 300) => {
    return (target, propertyName, descriptor) => {
        Reflect.defineMetadata('cache_ttl', ttl, descriptor.value);
        Reflect.defineMetadata('cache_enabled', true, descriptor.value);
    };
};
exports.CacheResponse = CacheResponse;
let CachingInterceptor = class CachingInterceptor {
    reflector;
    redis;
    constructor(reflector, redis) {
        this.reflector = reflector;
        this.redis = redis;
    }
    async intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const isCacheEnabled = this.reflector.get('cache_enabled', handler);
        const cacheTTL = this.reflector.get('cache_ttl', handler) || 300;
        if (!isCacheEnabled || request.method !== 'GET') {
            return next.handle();
        }
        const cacheKey = this.generateCacheKey(request);
        try {
            const cachedResult = await this.redis.get(cacheKey);
            if (cachedResult) {
                const response = context.switchToHttp().getResponse();
                response.set('X-Cache', 'HIT');
                response.set('X-Cache-Key', cacheKey);
                return (0, rxjs_1.of)(JSON.parse(cachedResult));
            }
            return next.handle().pipe((0, operators_1.tap)(async (data) => {
                try {
                    await this.redis.setex(cacheKey, cacheTTL, JSON.stringify(data));
                    const response = context.switchToHttp().getResponse();
                    response.set('X-Cache', 'MISS');
                    response.set('X-Cache-Key', cacheKey);
                }
                catch (error) {
                    console.error('Cache set error:', error);
                }
            }));
        }
        catch (error) {
            return next.handle();
        }
    }
    generateCacheKey(request) {
        const { method, originalUrl, user } = request;
        const userId = user?.id || 'anonymous';
        const crypto = require('crypto');
        const keyString = `${method}:${originalUrl}:${userId}`;
        const hash = crypto.createHash('md5').update(keyString).digest('hex');
        return `api_cache:${hash}`;
    }
};
exports.CachingInterceptor = CachingInterceptor;
exports.CachingInterceptor = CachingInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)(redis_module_1.REDIS_CLIENT)),
    __metadata("design:paramtypes", [core_1.Reflector,
        ioredis_1.Redis])
], CachingInterceptor);
//# sourceMappingURL=caching.interceptor.js.map