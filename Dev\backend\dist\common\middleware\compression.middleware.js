"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompressionMiddleware = void 0;
const common_1 = require("@nestjs/common");
const compression_1 = __importDefault(require("compression"));
let CompressionMiddleware = class CompressionMiddleware {
    compression = (0, compression_1.default)({
        threshold: 1024,
        level: 6,
        memLevel: 8,
        filter: (req, res) => {
            if (req.headers['x-no-compression']) {
                return false;
            }
            const contentType = res.getHeader('content-type');
            if (contentType) {
                return /text|json|javascript|css|xml|svg/.test(contentType);
            }
            return compression_1.default.filter(req, res);
        },
        strategy: 0,
        windowBits: 15,
        chunkSize: 16384,
    });
    use(req, res, next) {
        res.setHeader('Vary', 'Accept-Encoding');
        if (req.path.startsWith('/api/')) {
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');
        }
        this.compression(req, res, next);
    }
};
exports.CompressionMiddleware = CompressionMiddleware;
exports.CompressionMiddleware = CompressionMiddleware = __decorate([
    (0, common_1.Injectable)()
], CompressionMiddleware);
//# sourceMappingURL=compression.middleware.js.map