"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ai_service_1 = require("./ai.service");
const caching_interceptor_1 = require("../../common/interceptors/caching.interceptor");
const rate_limit_guard_1 = require("../../common/guards/rate-limit.guard");
const response_time_interceptor_1 = require("../../common/interceptors/response-time.interceptor");
let AiController = class AiController {
    aiService;
    constructor(aiService) {
        this.aiService = aiService;
    }
    async translateText(request) {
        try {
            if (!request.text || !request.sourceLanguage || !request.targetLanguage) {
                throw new common_1.HttpException('Missing required parameters', common_1.HttpStatus.BAD_REQUEST);
            }
            if (request.text.length > 10000) {
                throw new common_1.HttpException('Text too long (max 10000 characters)', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.aiService.translateText(request);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new common_1.HttpException(`Translation failed: ${errorMessage}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateAnswer(request) {
        try {
            if (!request.question) {
                throw new common_1.HttpException('Question is required', common_1.HttpStatus.BAD_REQUEST);
            }
            if (request.question.length > 1000) {
                throw new common_1.HttpException('Question too long (max 1000 characters)', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.aiService.generateAnswer(request.question, request.userId);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new common_1.HttpException(`QA generation failed: ${errorMessage}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async detectQuestion(body) {
        try {
            if (!body.text) {
                throw new common_1.HttpException('Text is required', common_1.HttpStatus.BAD_REQUEST);
            }
            const isQuestion = await this.aiService.isQuestionDetected(body.text);
            return { isQuestion };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new common_1.HttpException(`Question detection failed: ${errorMessage}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async summarizeText(request) {
        try {
            if (!request.text) {
                throw new common_1.HttpException('Text is required', common_1.HttpStatus.BAD_REQUEST);
            }
            if (request.text.length < 100) {
                throw new common_1.HttpException('Text too short for summarization (min 100 characters)', common_1.HttpStatus.BAD_REQUEST);
            }
            if (request.text.length > 100000) {
                throw new common_1.HttpException('Text too long (max 100000 characters)', common_1.HttpStatus.BAD_REQUEST);
            }
            return await this.aiService.summarizeText(request);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new common_1.HttpException(`Summarization failed: ${errorMessage}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getHealthStatus() {
        try {
            return await this.aiService.getHealthStatus();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new common_1.HttpException(`Health check failed: ${errorMessage}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AiController = AiController;
__decorate([
    (0, common_1.Post)('translate'),
    (0, swagger_1.ApiOperation)({ summary: 'Translate text using multi-model AI architecture' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Translation completed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid request parameters' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Translation service error' }),
    (0, rate_limit_guard_1.RateLimit)(20, 60000),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                text: { type: 'string', description: 'Text to translate' },
                sourceLanguage: { type: 'string', description: 'Source language code (e.g., en-US, zh-CN)' },
                targetLanguage: { type: 'string', description: 'Target language code (e.g., en-US, zh-CN)' },
                priority: { type: 'string', enum: ['speed', 'quality', 'balanced'], description: 'Translation priority' },
            },
            required: ['text', 'sourceLanguage', 'targetLanguage'],
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AiController.prototype, "translateText", null);
__decorate([
    (0, common_1.Post)('qa'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate AI-powered answers to questions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Answer generated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid request parameters' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'QA service error' }),
    (0, rate_limit_guard_1.RateLimit)(15, 60000),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                question: { type: 'string', description: 'Question to answer' },
                language: { type: 'string', enum: ['english', 'chinese', 'bilingual'], description: 'Response language' },
                context: { type: 'string', description: 'Additional context for the question' },
                userId: { type: 'string', description: 'User ID for tracking' },
            },
            required: ['question'],
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AiController.prototype, "generateAnswer", null);
__decorate([
    (0, common_1.Post)('detect-question'),
    (0, swagger_1.ApiOperation)({ summary: 'Detect if text contains a question' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Question detection completed' }),
    (0, rate_limit_guard_1.RateLimit)(60, 60000),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                text: { type: 'string', description: 'Text to analyze' },
            },
            required: ['text'],
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AiController.prototype, "detectQuestion", null);
__decorate([
    (0, common_1.Post)('summarize'),
    (0, swagger_1.ApiOperation)({ summary: 'Summarize text using long-context AI models' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Summarization completed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid request parameters' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Summarization service error' }),
    (0, rate_limit_guard_1.RateLimit)(10, 60000),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                text: { type: 'string', description: 'Text to summarize' },
                language: { type: 'string', enum: ['english', 'chinese', 'bilingual'], description: 'Summary language' },
                type: { type: 'string', enum: ['brief', 'detailed', 'bullet_points', 'key_concepts'], description: 'Summary type' },
                maxLength: { type: 'number', description: 'Maximum summary length' },
            },
            required: ['text'],
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AiController.prototype, "summarizeText", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Get AI services health status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Health status retrieved successfully' }),
    (0, caching_interceptor_1.CacheResponse)(60),
    (0, rate_limit_guard_1.RateLimit)(10, 60000),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AiController.prototype, "getHealthStatus", null);
exports.AiController = AiController = __decorate([
    (0, swagger_1.ApiTags)('AI Services'),
    (0, common_1.Controller)('ai'),
    (0, common_1.UseGuards)(rate_limit_guard_1.RateLimitGuard),
    (0, common_1.UseInterceptors)(response_time_interceptor_1.ResponseTimeInterceptor, caching_interceptor_1.CachingInterceptor),
    __metadata("design:paramtypes", [ai_service_1.AiService])
], AiController);
//# sourceMappingURL=ai.controller.js.map