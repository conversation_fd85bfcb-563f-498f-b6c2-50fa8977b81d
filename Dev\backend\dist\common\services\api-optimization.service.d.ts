interface ApiMetrics {
    endpoint: string;
    method: string;
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    cacheHitRate: number;
    lastResetTime: number;
}
export declare class ApiOptimizationService {
    private readonly logger;
    private readonly metrics;
    private readonly resetInterval;
    constructor();
    recordRequest(endpoint: string, method: string, responseTime: number, isError?: boolean, isCacheHit?: boolean): void;
    getMetrics(): ApiMetrics[];
    getProblematicEndpoints(): {
        slowEndpoints: ApiMetrics[];
        highErrorEndpoints: ApiMetrics[];
        lowCacheEndpoints: ApiMetrics[];
    };
    getOptimizationSuggestions(): string[];
    private resetMetrics;
    exportDetailedReport(): {
        summary: {
            totalEndpoints: number;
            totalRequests: number;
            averageResponseTime: number;
            overallErrorRate: number;
            overallCacheHitRate: number;
        };
        metrics: ApiMetrics[];
        problematic: {
            slowEndpoints: ApiMetrics[];
            highErrorEndpoints: ApiMetrics[];
            lowCacheEndpoints: ApiMetrics[];
        };
        suggestions: string[];
    };
}
export {};
