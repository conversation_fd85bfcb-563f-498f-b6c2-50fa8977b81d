import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ApiOptimizationService } from '../services/api-optimization.service';
export declare class ResponseTimeInterceptor implements NestInterceptor {
    private readonly apiOptimizationService?;
    private readonly logger;
    constructor(apiOptimizationService?: ApiOptimizationService | undefined);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
