{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm init:*)", "Bash(npm i:*)", "Bash(nest new:*)", "Bash(rm:*)", "Bash(npm create:*)", "Bash(npx tailwindcss init:*)", "Bash(npm run start:dev:*)", "Bash(timeout 5 npm run start:dev)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(pg_ctl:*)", "Bash(\"C:\\Program Files\\PostgreSQL\\17\\bin\\pg_ctl.exe\" start -D \"C:\\Program Files\\PostgreSQL\\17\\data\")", "<PERSON><PERSON>(net start:*)"], "deny": []}}