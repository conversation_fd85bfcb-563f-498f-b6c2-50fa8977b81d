"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationCacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("../../../common/services/cache.service");
let TranslationCacheService = class TranslationCacheService {
    cacheService;
    TRANSLATION_NAMESPACE = 'translation';
    STATS_NAMESPACE = 'stats';
    HISTORY_NAMESPACE = 'history';
    CACHE_TTL = {
        TRANSLATION: 24 * 60 * 60,
        STATS: 60 * 60,
        HISTORY: 30 * 60,
        LANGUAGE_PAIRS: 7 * 24 * 60 * 60,
    };
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    generateTranslationKey(sourceText, sourceLang, targetLang) {
        const hash = this.simpleHash(sourceText + sourceLang + targetLang);
        return `${sourceLang}_${targetLang}_${hash}`;
    }
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }
    async cacheTranslation(sourceText, sourceLang, targetLang, result) {
        const key = this.generateTranslationKey(sourceText, sourceLang, targetLang);
        await this.cacheService.set(key, result, {
            ttl: this.CACHE_TTL.TRANSLATION,
            namespace: this.TRANSLATION_NAMESPACE,
        });
    }
    async getCachedTranslation(sourceText, sourceLang, targetLang) {
        const key = this.generateTranslationKey(sourceText, sourceLang, targetLang);
        return await this.cacheService.get(key, {
            namespace: this.TRANSLATION_NAMESPACE,
        });
    }
    async cacheUserStats(userId, stats) {
        const key = `user_${userId}`;
        await this.cacheService.set(key, stats, {
            ttl: this.CACHE_TTL.STATS,
            namespace: this.STATS_NAMESPACE,
        });
    }
    async getCachedUserStats(userId) {
        const key = `user_${userId}`;
        return await this.cacheService.get(key, {
            namespace: this.STATS_NAMESPACE,
        });
    }
    async cacheUserHistory(userId, page, limit, filters, history) {
        const filterKey = this.generateFilterKey(filters);
        const key = `user_${userId}_p${page}_l${limit}_${filterKey}`;
        await this.cacheService.set(key, history, {
            ttl: this.CACHE_TTL.HISTORY,
            namespace: this.HISTORY_NAMESPACE,
        });
    }
    async getCachedUserHistory(userId, page, limit, filters) {
        const filterKey = this.generateFilterKey(filters);
        const key = `user_${userId}_p${page}_l${limit}_${filterKey}`;
        return await this.cacheService.get(key, {
            namespace: this.HISTORY_NAMESPACE,
        });
    }
    generateFilterKey(filters) {
        const filterStr = JSON.stringify(filters || {});
        return this.simpleHash(filterStr);
    }
    async clearUserCache(userId) {
        await this.cacheService.del(`user_${userId}`, {
            namespace: this.STATS_NAMESPACE,
        });
        await this.cacheService.delPattern(`user_${userId}_*`, {
            namespace: this.HISTORY_NAMESPACE,
        });
    }
    async clearTranslationCache() {
        await this.cacheService.flush(this.TRANSLATION_NAMESPACE);
    }
    async cachePopularLanguagePairs(pairs) {
        const key = 'popular_pairs';
        await this.cacheService.set(key, pairs, {
            ttl: this.CACHE_TTL.LANGUAGE_PAIRS,
            namespace: this.STATS_NAMESPACE,
        });
    }
    async getCachedPopularLanguagePairs() {
        const key = 'popular_pairs';
        return await this.cacheService.get(key, {
            namespace: this.STATS_NAMESPACE,
        });
    }
    async preloadTranslations(translations) {
        const cacheItems = translations.map(item => ({
            key: this.generateTranslationKey(item.sourceText, item.sourceLang, item.targetLang),
            value: item.result,
            ttl: this.CACHE_TTL.TRANSLATION,
        }));
        await this.cacheService.mset(cacheItems, {
            namespace: this.TRANSLATION_NAMESPACE,
        });
    }
    async getCacheStats() {
        try {
            const [translationKeys, statsKeys, historyKeys] = await Promise.all([
                this.cacheService.get('cache_size_translation') || 0,
                this.cacheService.get('cache_size_stats') || 0,
                this.cacheService.get('cache_size_history') || 0,
            ]);
            return {
                translationCacheSize: translationKeys,
                statsCacheSize: statsKeys,
                historyCacheSize: historyKeys,
            };
        }
        catch (error) {
            return {
                translationCacheSize: 0,
                statsCacheSize: 0,
                historyCacheSize: 0,
            };
        }
    }
    async smartCleanup() {
    }
};
exports.TranslationCacheService = TranslationCacheService;
exports.TranslationCacheService = TranslationCacheService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], TranslationCacheService);
//# sourceMappingURL=translation-cache.service.js.map