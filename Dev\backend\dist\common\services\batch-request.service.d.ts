interface BatchRequest {
    id: string;
    data: any;
    resolve: (value: any) => void;
    reject: (error: any) => void;
    timestamp: number;
}
interface BatchConfig {
    maxBatchSize: number;
    maxWaitTime: number;
    processor: (requests: BatchRequest[]) => Promise<any[]>;
}
export declare class BatchRequestService {
    private readonly logger;
    private batches;
    registerBatch(batchKey: string, config: BatchConfig): void;
    addToBatch<T>(batchKey: string, requestId: string, data: any): Promise<T>;
    private processBatch;
    getBatchStats(): Record<string, {
        pendingRequests: number;
        hasTimer: boolean;
        maxBatchSize: number;
        maxWaitTime: number;
    }>;
    cleanupExpiredRequests(): void;
}
export declare class TranslationBatchService {
    private readonly batchService;
    constructor(batchService: BatchRequestService);
    batchTranslate(text: string, sourceLang: string, targetLang: string): Promise<string>;
    private processTranslationBatch;
}
export {};
