"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbPerformanceConfig = exports.getDatabasePerformanceConfig = void 0;
const getDatabasePerformanceConfig = () => {
    return {
        extra: {
            max: parseInt(process.env.DB_POOL_MAX || '20'),
            min: parseInt(process.env.DB_POOL_MIN || '5'),
            acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
            createTimeoutMillis: parseInt(process.env.DB_CREATE_TIMEOUT || '30000'),
            destroyTimeoutMillis: parseInt(process.env.DB_DESTROY_TIMEOUT || '5000'),
            idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '300000'),
            reapIntervalMillis: parseInt(process.env.DB_REAP_INTERVAL || '1000'),
            createRetryIntervalMillis: parseInt(process.env.DB_CREATE_RETRY_INTERVAL || '200'),
            log: process.env.NODE_ENV === 'development',
        },
        cache: {
            type: 'redis',
            options: {
                host: process.env.REDIS_HOST || 'localhost',
                port: parseInt(process.env.REDIS_PORT || '6379'),
                password: process.env.REDIS_PASSWORD,
                db: parseInt(process.env.REDIS_CACHE_DB || '1'),
            },
            duration: parseInt(process.env.DB_CACHE_DURATION || '300000'),
            ignoreErrors: true,
        },
        maxQueryExecutionTime: parseInt(process.env.DB_MAX_QUERY_TIME || '5000'),
        connectTimeoutMS: parseInt(process.env.DB_CONNECT_TIMEOUT || '10000'),
        socketTimeoutMS: parseInt(process.env.DB_SOCKET_TIMEOUT || '0'),
        logging: process.env.NODE_ENV === 'development' ? 'all' : ['error', 'warn'],
        logger: 'advanced-console',
        entities: ['dist/**/*.entity{.ts,.js}'],
        migrations: ['dist/database/migrations/*{.ts,.js}'],
        migrationsRun: process.env.NODE_ENV !== 'production',
        synchronize: false,
        subscribers: ['dist/**/*.subscriber{.ts,.js}'],
    };
};
exports.getDatabasePerformanceConfig = getDatabasePerformanceConfig;
exports.dbPerformanceConfig = {
    slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000'),
    poolMonitorInterval: parseInt(process.env.POOL_MONITOR_INTERVAL || '30000'),
    poolUtilizationWarning: parseFloat(process.env.POOL_UTILIZATION_WARNING || '80'),
    enableQueryAnalysis: process.env.ENABLE_QUERY_ANALYSIS === 'true',
    queryAnalysisSampleRate: parseFloat(process.env.QUERY_ANALYSIS_SAMPLE_RATE || '0.1'),
};
//# sourceMappingURL=database-performance.config.js.map