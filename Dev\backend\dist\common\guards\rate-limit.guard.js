"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitGuard = exports.RateLimit = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const redis_module_1 = require("../../modules/redis/redis.module");
const ioredis_1 = require("ioredis");
const RateLimit = (limit, windowMs = 60000) => {
    return (target, propertyName, descriptor) => {
        Reflect.defineMetadata('rate_limit', limit, descriptor.value);
        Reflect.defineMetadata('rate_window', windowMs, descriptor.value);
    };
};
exports.RateLimit = RateLimit;
let RateLimitGuard = class RateLimitGuard {
    reflector;
    redis;
    constructor(reflector, redis) {
        this.reflector = reflector;
        this.redis = redis;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const limit = this.reflector.get('rate_limit', handler);
        const windowMs = this.reflector.get('rate_window', handler) || 60000;
        if (!limit) {
            return true;
        }
        const key = this.generateKey(request);
        const window = Math.floor(Date.now() / windowMs);
        const redisKey = `rate_limit:${key}:${window}`;
        try {
            const current = await this.redis.incr(redisKey);
            if (current === 1) {
                await this.redis.expire(redisKey, Math.ceil(windowMs / 1000));
            }
            if (current > limit) {
                throw new common_1.HttpException({
                    statusCode: common_1.HttpStatus.TOO_MANY_REQUESTS,
                    message: 'Too Many Requests',
                    error: 'Rate Limit Exceeded',
                    retryAfter: Math.ceil(windowMs / 1000),
                }, common_1.HttpStatus.TOO_MANY_REQUESTS);
            }
            const response = context.switchToHttp().getResponse();
            response.set('X-RateLimit-Limit', limit.toString());
            response.set('X-RateLimit-Remaining', Math.max(0, limit - current).toString());
            response.set('X-RateLimit-Reset', (Date.now() + windowMs).toString());
            return true;
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            console.error('Rate limit Redis error:', error);
            return true;
        }
    }
    generateKey(request) {
        const userId = request.user?.id;
        const ip = request.ip || request.connection.remoteAddress;
        return userId ? `user:${userId}` : `ip:${ip}`;
    }
};
exports.RateLimitGuard = RateLimitGuard;
exports.RateLimitGuard = RateLimitGuard = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)(redis_module_1.REDIS_CLIENT)),
    __metadata("design:paramtypes", [core_1.Reflector,
        ioredis_1.Redis])
], RateLimitGuard);
//# sourceMappingURL=rate-limit.guard.js.map