import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
export interface CacheOptions {
    ttl?: number;
    namespace?: string;
}
export declare class CacheService {
    private readonly redis;
    private readonly configService;
    private readonly defaultTTL;
    private readonly defaultNamespace;
    constructor(redis: Redis, configService: ConfigService);
    private generateKey;
    set(key: string, value: any, options?: CacheOptions): Promise<void>;
    get<T>(key: string, options?: CacheOptions): Promise<T | null>;
    del(key: string, options?: CacheOptions): Promise<void>;
    delPattern(pattern: string, options?: CacheOptions): Promise<void>;
    exists(key: string, options?: CacheOptions): Promise<boolean>;
    expire(key: string, ttl: number, options?: CacheOptions): Promise<void>;
    mget<T>(keys: string[], options?: CacheOptions): Promise<(T | null)[]>;
    mset(keyValuePairs: Array<{
        key: string;
        value: any;
        ttl?: number;
    }>, options?: CacheOptions): Promise<void>;
    incr(key: string, options?: CacheOptions): Promise<number>;
    incrby(key: string, increment: number, options?: CacheOptions): Promise<number>;
    ttl(key: string, options?: CacheOptions): Promise<number>;
    flush(namespace?: string): Promise<void>;
    isConnected(): boolean;
    getInfo(): Promise<any>;
}
