import { Repository, DataSource } from 'typeorm';
import { AiService } from '../ai/ai.service';
import { Translation } from './entities/translation.entity';
import { User } from '../auth/entities/user.entity';
import { TranslateDto, TranslationQueryDto } from './dto';
import { TranslationResult, TranslationHistoryResponse, TranslationStats } from './interfaces';
import { TranslationCacheService } from './services/translation-cache.service';
export declare class TranslationService {
    private readonly translationRepository;
    private readonly userRepository;
    private readonly aiService;
    private readonly dataSource;
    private readonly cacheService;
    constructor(translationRepository: Repository<Translation>, userRepository: Repository<User>, aiService: AiService, dataSource: DataSource, cacheService: TranslationCacheService);
    translate(translateDto: TranslateDto, userId: string): Promise<TranslationResult>;
    getHistory(userId: string, query: TranslationQueryDto): Promise<TranslationHistoryResponse>;
    getStats(userId: string): Promise<TranslationStats>;
    toggleFavorite(translationId: string, userId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    deleteTranslation(translationId: string, userId: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
