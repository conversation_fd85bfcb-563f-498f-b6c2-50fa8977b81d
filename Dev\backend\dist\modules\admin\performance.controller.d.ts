import { ApiOptimizationService } from '../../common/services/api-optimization.service';
import { CacheService } from '../../common/services/cache.service';
import { DataSource } from 'typeorm';
export declare class PerformanceController {
    private readonly apiOptimizationService;
    private readonly cacheService;
    private readonly dataSource;
    constructor(apiOptimizationService: ApiOptimizationService, cacheService: CacheService, dataSource: DataSource);
    getApiMetrics(): Promise<any>;
    getDatabaseMetrics(): Promise<{
        connectionPool: {
            totalConnections: any;
            freeConnections: any;
            usedConnections: number;
            utilizationRate: string;
        } | null;
        isConnected: boolean;
        databaseName: string | Uint8Array<ArrayBufferLike> | undefined;
        driverType: "mysql" | "mariadb" | "postgres" | "cockroachdb" | "sqlite" | "mssql" | "sap" | "oracle" | "cordova" | "nativescript" | "react-native" | "sqljs" | "mongodb" | "aurora-mysql" | "aurora-postgres" | "expo" | "better-sqlite3" | "capacitor" | "spanner";
    }>;
    getCacheMetrics(): Promise<{
        message: string;
        timestamp: string;
    }>;
    getSystemMetrics(): Promise<{
        memory: {
            rss: string;
            heapTotal: string;
            heapUsed: string;
            external: string;
            arrayBuffers: string;
        };
        cpu: {
            user: number;
            system: number;
        };
        uptime: string;
        nodeVersion: string;
        platform: NodeJS.Platform;
        architecture: NodeJS.Architecture;
    }>;
    getHealthSummary(): Promise<{
        status: string;
        healthScore: number;
        summary: {
            api: {
                totalEndpoints: number;
                averageResponseTime: number;
                errorRate: string;
                cacheHitRate: string;
            };
            database: {
                connected: boolean;
                poolUtilization: string;
            };
            system: {
                memoryUsage: string;
                uptime: string;
            };
        };
        recommendations: string[];
        timestamp: string;
    }>;
    private calculateHealthScore;
    private generateSystemRecommendations;
}
