import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';
import { Redis } from 'ioredis';
export declare const CacheResponse: (ttl?: number) => (target: any, propertyName: string, descriptor: PropertyDescriptor) => void;
export declare class CachingInterceptor implements NestInterceptor {
    private reflector;
    private readonly redis;
    constructor(reflector: Reflector, redis: Redis);
    intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>>;
    private generateCacheKey;
}
