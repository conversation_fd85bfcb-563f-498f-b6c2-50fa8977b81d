"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiOptimizationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiOptimizationService = void 0;
const common_1 = require("@nestjs/common");
let ApiOptimizationService = ApiOptimizationService_1 = class ApiOptimizationService {
    logger = new common_1.Logger(ApiOptimizationService_1.name);
    metrics = new Map();
    resetInterval = 3600000;
    constructor() {
        setInterval(() => this.resetMetrics(), this.resetInterval);
    }
    recordRequest(endpoint, method, responseTime, isError = false, isCacheHit = false) {
        const key = `${method}:${endpoint}`;
        const stats = this.metrics.get(key) || {
            totalTime: 0,
            count: 0,
            errors: 0,
            cacheHits: 0,
            cacheMisses: 0,
        };
        stats.totalTime += responseTime;
        stats.count += 1;
        if (isError) {
            stats.errors += 1;
        }
        if (isCacheHit) {
            stats.cacheHits += 1;
        }
        else {
            stats.cacheMisses += 1;
        }
        this.metrics.set(key, stats);
        if (responseTime > 2000) {
            this.logger.warn(`Slow API request: ${key} - ${responseTime}ms`);
        }
    }
    getMetrics() {
        const result = [];
        for (const [key, stats] of this.metrics.entries()) {
            const [method, endpoint] = key.split(':', 2);
            result.push({
                endpoint,
                method,
                totalRequests: stats.count,
                averageResponseTime: stats.count > 0 ? stats.totalTime / stats.count : 0,
                errorRate: stats.count > 0 ? (stats.errors / stats.count) * 100 : 0,
                cacheHitRate: (stats.cacheHits + stats.cacheMisses) > 0
                    ? (stats.cacheHits / (stats.cacheHits + stats.cacheMisses)) * 100
                    : 0,
                lastResetTime: Date.now() - this.resetInterval,
            });
        }
        return result.sort((a, b) => b.totalRequests - a.totalRequests);
    }
    getProblematicEndpoints() {
        const metrics = this.getMetrics();
        return {
            slowEndpoints: metrics
                .filter(m => m.averageResponseTime > 1000 && m.totalRequests > 10)
                .sort((a, b) => b.averageResponseTime - a.averageResponseTime)
                .slice(0, 10),
            highErrorEndpoints: metrics
                .filter(m => m.errorRate > 5 && m.totalRequests > 10)
                .sort((a, b) => b.errorRate - a.errorRate)
                .slice(0, 10),
            lowCacheEndpoints: metrics
                .filter(m => m.cacheHitRate < 30 && m.totalRequests > 10 && m.method === 'GET')
                .sort((a, b) => a.cacheHitRate - b.cacheHitRate)
                .slice(0, 10),
        };
    }
    getOptimizationSuggestions() {
        const suggestions = [];
        const problematic = this.getProblematicEndpoints();
        if (problematic.slowEndpoints.length > 0) {
            suggestions.push(`发现 ${problematic.slowEndpoints.length} 个慢响应端点，建议优化数据库查询或增加缓存`);
        }
        if (problematic.highErrorEndpoints.length > 0) {
            suggestions.push(`发现 ${problematic.highErrorEndpoints.length} 个高错误率端点，建议检查错误处理和输入验证`);
        }
        if (problematic.lowCacheEndpoints.length > 0) {
            suggestions.push(`发现 ${problematic.lowCacheEndpoints.length} 个低缓存命中率的GET端点，建议增加缓存策略`);
        }
        const totalRequests = Array.from(this.metrics.values())
            .reduce((sum, stats) => sum + stats.count, 0);
        if (totalRequests > 10000) {
            suggestions.push('请求量较大，建议考虑负载均衡和水平扩展');
        }
        return suggestions;
    }
    resetMetrics() {
        const totalRequests = Array.from(this.metrics.values())
            .reduce((sum, stats) => sum + stats.count, 0);
        if (totalRequests > 0) {
            this.logger.log(`API性能指标重置，上周期处理 ${totalRequests} 个请求`);
            const problematic = this.getProblematicEndpoints();
            if (problematic.slowEndpoints.length > 0 ||
                problematic.highErrorEndpoints.length > 0) {
                this.logger.warn('发现性能问题端点', problematic);
            }
        }
        this.metrics.clear();
    }
    exportDetailedReport() {
        const metrics = this.getMetrics();
        const totalRequests = metrics.reduce((sum, m) => sum + m.totalRequests, 0);
        const totalTime = metrics.reduce((sum, m) => sum + (m.averageResponseTime * m.totalRequests), 0);
        const totalErrors = metrics.reduce((sum, m) => sum + (m.totalRequests * m.errorRate / 100), 0);
        const cacheMetrics = metrics.filter(m => m.method === 'GET');
        const totalCacheRequests = cacheMetrics.reduce((sum, m) => sum + m.totalRequests, 0);
        const totalCacheHits = cacheMetrics.reduce((sum, m) => sum + (m.totalRequests * m.cacheHitRate / 100), 0);
        return {
            summary: {
                totalEndpoints: metrics.length,
                totalRequests,
                averageResponseTime: totalRequests > 0 ? totalTime / totalRequests : 0,
                overallErrorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
                overallCacheHitRate: totalCacheRequests > 0 ? (totalCacheHits / totalCacheRequests) * 100 : 0,
            },
            metrics,
            problematic: this.getProblematicEndpoints(),
            suggestions: this.getOptimizationSuggestions(),
        };
    }
};
exports.ApiOptimizationService = ApiOptimizationService;
exports.ApiOptimizationService = ApiOptimizationService = ApiOptimizationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ApiOptimizationService);
//# sourceMappingURL=api-optimization.service.js.map