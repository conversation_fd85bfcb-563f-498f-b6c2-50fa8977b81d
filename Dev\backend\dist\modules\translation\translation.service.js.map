{"version": 3, "file": "translation.service.js", "sourceRoot": "", "sources": ["../../../src/modules/translation/translation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAiD;AACjD,iDAA6C;AAC7C,sEAA4D;AAC5D,8DAAoD;AAGpD,oFAA+E;AAGxE,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAGV;IAEA;IACA;IACA;IACA;IAPnB,YAEmB,qBAA8C,EAE9C,cAAgC,EAChC,SAAoB,EACpB,UAAsB,EACtB,YAAqC;QALrC,0BAAqB,GAArB,qBAAqB,CAAyB;QAE9C,mBAAc,GAAd,cAAc,CAAkB;QAChC,cAAS,GAAT,SAAS,CAAW;QACpB,eAAU,GAAV,UAAU,CAAY;QACtB,iBAAY,GAAZ,YAAY,CAAyB;IACrD,CAAC;IAEJ,KAAK,CAAC,SAAS,CAAC,YAA0B,EAAE,MAAc;QACxD,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC;QAG/D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAChG,IAAI,YAAY,EAAE,CAAC;YAEjB,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBACzD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,gCAAW,EAAE;oBAC9C,MAAM;oBACN,UAAU,EAAE,IAAI;oBAChB,cAAc,EAAE,YAAY,CAAC,cAAc;oBAC3C,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,UAAU;oBACtB,QAAQ,EAAE,EAAE;oBACZ,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,YAAY,CAAC,UAAU,IAAI,IAAI;iBAC5C,CAAC,CAAC;gBAEH,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,gCAAW,EAAE,WAAW,CAAC,CAAC;gBAEtE,OAAO;oBACL,GAAG,YAAY;oBACf,EAAE,EAAE,gBAAgB,CAAC,EAAE;oBACvB,SAAS,EAAE,gBAAgB,CAAC,SAAS;iBACtC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAEzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;gBAChD,IAAI;gBACJ,cAAc,EAAE,UAAU;gBAC1B,cAAc,EAAE,UAAU;gBAC1B,QAAQ,EAAE,UAAU;aACrB,CAAC,CAAC;YAGH,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,gCAAW,EAAE;gBAC9C,MAAM;gBACN,UAAU,EAAE,IAAI;gBAChB,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,MAAM,CAAC,OAAO;gBACxB,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,gCAAW,EAAE,WAAW,CAAC,CAAC;YAEtE,MAAM,iBAAiB,GAAsB;gBAC3C,EAAE,EAAE,gBAAgB,CAAC,EAAE;gBACvB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,cAAc,EAAE,UAAU;gBAC1B,cAAc,EAAE,UAAU;gBAC1B,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,gBAAgB,CAAC,SAAS;aACtC,CAAC;YAGF,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;YAE1F,OAAO,iBAAiB,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,KAA0B;QACzD,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QAG1E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC;QAC/H,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC;QACvB,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB;aAC5C,kBAAkB,CAAC,aAAa,CAAC;aACjC,MAAM,CAAC;YACN,gBAAgB;YAChB,wBAAwB;YACxB,4BAA4B;YAC5B,wBAAwB;YACxB,wBAAwB;YACxB,wBAAwB;YACxB,sBAAsB;YACtB,wBAAwB;YACxB,uBAAuB;SACxB,CAAC;aACD,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC;aACjD,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;QAG5C,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,QAAQ,CAAC,sCAAsC,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QACtF,CAAC;QAGD,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY;aAC7C,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;aACxB,IAAI,CAAC,KAAK,CAAC;aACX,eAAe,EAAE,CAAC;QAErB,MAAM,kBAAkB,GAAwB,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC/E,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,YAAY,EAAE,WAAW,CAAC,UAAU;YACpC,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,cAAc,EAAE,WAAW,CAAC,UAAU;YACtC,cAAc,EAAE,WAAW,CAAC,UAAU;YACtC,UAAU,EAAE,WAAW,CAAC,UAAU,IAAI,IAAI;YAC1C,OAAO,EAAE,WAAW,CAAC,QAAQ;YAC7B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,UAAU,EAAE,WAAW,CAAC,UAAU,IAAI,KAAK;SAC5C,CAAC,CAAC,CAAC;QAEJ,MAAM,MAAM,GAAG;YACb,YAAY,EAAE,kBAAkB;YAChC,KAAK;YACL,IAAI;YACJ,KAAK;YACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACrC,CAAC;QAGF,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;QAE7G,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAc;QAE3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvE,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,WAAW,CAAC;QACrB,CAAC;QAGD,MAAM,CAAC,KAAK,EAAE,cAAc,EAAE,iBAAiB,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAEnF,IAAI,CAAC,qBAAqB;iBACvB,kBAAkB,CAAC,aAAa,CAAC;iBACjC,MAAM,CAAC;gBACN,+BAA+B;gBAC/B,yCAAyC;gBACzC,kFAAkF;aACnF,CAAC;iBACD,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC;iBACjD,SAAS,EAAE;YAGd,IAAI,CAAC,qBAAqB;iBACvB,kBAAkB,CAAC,aAAa,CAAC;iBACjC,MAAM,CAAC,0EAA0E,EAAE,YAAY,CAAC;iBAChG,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC;iBACjD,SAAS,EAAE;YAGd,IAAI,CAAC,qBAAqB;iBACvB,kBAAkB,CAAC,aAAa,CAAC;iBACjC,MAAM,CAAC,wBAAwB,EAAE,YAAY,CAAC;iBAC9C,SAAS,CAAC,wBAAwB,EAAE,YAAY,CAAC;iBACjD,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;iBAC9B,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC;iBACjD,OAAO,CAAC,gDAAgD,CAAC;iBACzD,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;iBACxB,KAAK,CAAC,EAAE,CAAC;iBACT,UAAU,EAAE;YAGf,CAAC,GAAG,EAAE;gBACJ,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;gBAChC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBAEjD,OAAO,IAAI,CAAC,qBAAqB;qBAC9B,kBAAkB,CAAC,aAAa,CAAC;qBACjC,MAAM,CAAC,6BAA6B,EAAE,MAAM,CAAC;qBAC7C,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC;qBAC9B,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC;qBACjD,QAAQ,CAAC,wCAAwC,EAAE,EAAE,YAAY,EAAE,CAAC;qBACpE,OAAO,CAAC,6BAA6B,CAAC;qBACtC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;qBACtB,UAAU,EAAE,CAAC;YAClB,CAAC,CAAC,EAAE;SACL,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG;YACb,iBAAiB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC;YACpD,UAAU,EAAE,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;YAC7C,kBAAkB,EAAE,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC;YACtD,YAAY,EAAE,KAAK,CAAC,iBAAiB,GAAG,CAAC;gBACvC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACnF,CAAC,CAAC,CAAC;YACL,eAAe,EAAE,QAAQ,CAAC,cAAc,EAAE,UAAU,CAAC,IAAI,CAAC;YAC1D,aAAa,EAAE,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC5C,cAAc,EAAE,IAAI,CAAC,UAAU;gBAC/B,cAAc,EAAE,IAAI,CAAC,UAAU;gBAC/B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;aAC5B,CAAC,CAAC;YACH,cAAc,EAAE,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC9C,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;aAChC,CAAC,CAAC;SACJ,CAAC;QAGF,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEvD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE,MAAc;QACxD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;SACrC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,WAAW,CAAC,UAAU,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC;QACjD,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGnD,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE/C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;SACrD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,aAAqB,EAAE,MAAc;QAC3D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;SACrC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAGrD,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE/C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;CACF,CAAA;AAlRY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gCAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;qCADiB,oBAAU;QAEjB,oBAAU;QACf,sBAAS;QACR,oBAAU;QACR,mDAAuB;GAR7C,kBAAkB,CAkR9B"}