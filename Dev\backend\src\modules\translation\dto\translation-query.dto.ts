import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min, Max, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';
import { SupportedLanguage } from './translate.dto';

export class TranslationQueryDto {
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must not exceed 100' })
  limit?: number = 10;

  @ApiProperty({
    description: 'Filter by source language',
    example: 'en',
    enum: SupportedLanguage,
    required: false,
  })
  @IsOptional()
  @IsEnum(SupportedLanguage, { message: 'Invalid source language' })
  sourceLang?: SupportedLanguage;

  @ApiProperty({
    description: 'Filter by target language',
    example: 'zh',
    enum: SupportedLanguage,
    required: false,
  })
  @IsOptional()
  @IsEnum(SupportedLanguage, { message: 'Invalid target language' })
  targetLang?: SupportedLanguage;

  @ApiProperty({
    description: 'Search text in translations',
    example: 'hello',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;

  @ApiProperty({
    description: 'Filter favorites only',
    example: true,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  favorites?: boolean;
}
