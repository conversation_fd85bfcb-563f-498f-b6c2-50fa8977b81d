import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiOptimizationService } from '../../common/services/api-optimization.service';
import { CacheService } from '../../common/services/cache.service';
import { DataSource } from 'typeorm';

@ApiTags('Admin - Performance')
@Controller('admin/performance')
@UseGuards(JwtAuthGuard)
export class PerformanceController {
  constructor(
    private readonly apiOptimizationService: ApiOptimizationService,
    private readonly cacheService: CacheService,
    private readonly dataSource: DataSource
  ) {}

  @Get('api-metrics')
  @ApiOperation({ summary: 'Get API performance metrics' })
  @ApiResponse({ status: 200, description: 'API metrics retrieved successfully' })
  async getApiMetrics(): Promise<any> {
    return this.apiOptimizationService.exportDetailedReport();
  }

  @Get('database-metrics')
  @ApiOperation({ summary: 'Get database performance metrics' })
  @ApiResponse({ status: 200, description: 'Database metrics retrieved successfully' })
  async getDatabaseMetrics() {
    const connectionPool = (this.dataSource.driver as any).pool;
    
    let poolStats = null;
    if (connectionPool) {
      poolStats = {
        totalConnections: connectionPool.totalCount || 0,
        freeConnections: connectionPool.freeCount || 0,
        usedConnections: (connectionPool.totalCount || 0) - (connectionPool.freeCount || 0),
        utilizationRate: connectionPool.totalCount > 0 
          ? (((connectionPool.totalCount - connectionPool.freeCount) / connectionPool.totalCount) * 100).toFixed(2) + '%'
          : '0%',
      };
    }

    return {
      connectionPool: poolStats,
      isConnected: this.dataSource.isInitialized,
      databaseName: this.dataSource.options.database,
      driverType: this.dataSource.options.type,
    };
  }

  @Get('cache-metrics')
  @ApiOperation({ summary: 'Get cache performance metrics' })
  @ApiResponse({ status: 200, description: 'Cache metrics retrieved successfully' })
  async getCacheMetrics() {
    // 这里需要实现缓存统计方法
    return {
      message: 'Cache metrics not yet implemented',
      // redis: await this.cacheService.getStats(),
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system-metrics')
  @ApiOperation({ summary: 'Get system performance metrics' })
  @ApiResponse({ status: 200, description: 'System metrics retrieved successfully' })
  async getSystemMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) + ' MB',
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      uptime: Math.round(process.uptime()) + ' seconds',
      nodeVersion: process.version,
      platform: process.platform,
      architecture: process.arch,
    };
  }

  @Get('health-summary')
  @ApiOperation({ summary: 'Get overall system health summary' })
  @ApiResponse({ status: 200, description: 'Health summary retrieved successfully' })
  async getHealthSummary() {
    const [apiReport, dbMetrics, systemMetrics] = await Promise.all([
      this.apiOptimizationService.exportDetailedReport(),
      this.getDatabaseMetrics(),
      this.getSystemMetrics(),
    ]);

    // 评估系统健康状况
    const healthScore = this.calculateHealthScore(apiReport, dbMetrics, systemMetrics);
    const status = healthScore >= 80 ? 'healthy' : healthScore >= 60 ? 'warning' : 'critical';

    return {
      status,
      healthScore,
      summary: {
        api: {
          totalEndpoints: apiReport.summary.totalEndpoints,
          averageResponseTime: Math.round(apiReport.summary.averageResponseTime),
          errorRate: apiReport.summary.overallErrorRate.toFixed(2) + '%',
          cacheHitRate: apiReport.summary.overallCacheHitRate.toFixed(2) + '%',
        },
        database: {
          connected: dbMetrics.isConnected,
          poolUtilization: dbMetrics.connectionPool?.utilizationRate || 'N/A',
        },
        system: {
          memoryUsage: systemMetrics.memory.heapUsed,
          uptime: systemMetrics.uptime,
        },
      },
      recommendations: [
        ...apiReport.suggestions,
        ...this.generateSystemRecommendations(systemMetrics, dbMetrics),
      ],
      timestamp: new Date().toISOString(),
    };
  }

  private calculateHealthScore(apiReport: any, dbMetrics: any, systemMetrics: any): number {
    let score = 100;

    // API性能评分 (40%)
    if (apiReport.summary.averageResponseTime > 2000) score -= 20;
    else if (apiReport.summary.averageResponseTime > 1000) score -= 10;
    
    if (apiReport.summary.overallErrorRate > 10) score -= 20;
    else if (apiReport.summary.overallErrorRate > 5) score -= 10;

    // 数据库连接评分 (30%)
    if (!dbMetrics.isConnected) score -= 30;
    else if (dbMetrics.connectionPool) {
      const utilization = parseFloat(dbMetrics.connectionPool.utilizationRate);
      if (utilization > 90) score -= 15;
      else if (utilization > 80) score -= 10;
    }

    // 系统资源评分 (30%)
    const heapUsed = parseFloat(systemMetrics.memory.heapUsed);
    const heapTotal = parseFloat(systemMetrics.memory.heapTotal);
    const memoryUtilization = (heapUsed / heapTotal) * 100;
    
    if (memoryUtilization > 90) score -= 15;
    else if (memoryUtilization > 80) score -= 10;

    return Math.max(0, score);
  }

  private generateSystemRecommendations(systemMetrics: any, dbMetrics: any): string[] {
    const recommendations: string[] = [];

    const heapUsed = parseFloat(systemMetrics.memory.heapUsed);
    const heapTotal = parseFloat(systemMetrics.memory.heapTotal);
    const memoryUtilization = (heapUsed / heapTotal) * 100;

    if (memoryUtilization > 80) {
      recommendations.push('内存使用率过高，建议优化内存使用或增加服务器内存');
    }

    if (dbMetrics.connectionPool) {
      const utilization = parseFloat(dbMetrics.connectionPool.utilizationRate);
      if (utilization > 80) {
        recommendations.push('数据库连接池使用率高，建议优化查询或增加连接池大小');
      }
    }

    const uptime = parseInt(systemMetrics.uptime);
    if (uptime > 30 * 24 * 3600) { // 30 days
      recommendations.push('服务运行时间较长，建议定期重启以清理内存碎片');
    }

    return recommendations;
  }
}