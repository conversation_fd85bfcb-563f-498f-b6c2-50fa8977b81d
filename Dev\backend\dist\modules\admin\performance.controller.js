"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const api_optimization_service_1 = require("../../common/services/api-optimization.service");
const cache_service_1 = require("../../common/services/cache.service");
const typeorm_1 = require("typeorm");
let PerformanceController = class PerformanceController {
    apiOptimizationService;
    cacheService;
    dataSource;
    constructor(apiOptimizationService, cacheService, dataSource) {
        this.apiOptimizationService = apiOptimizationService;
        this.cacheService = cacheService;
        this.dataSource = dataSource;
    }
    async getApiMetrics() {
        return this.apiOptimizationService.exportDetailedReport();
    }
    async getDatabaseMetrics() {
        const connectionPool = this.dataSource.driver.pool;
        let poolStats = null;
        if (connectionPool) {
            poolStats = {
                totalConnections: connectionPool.totalCount || 0,
                freeConnections: connectionPool.freeCount || 0,
                usedConnections: (connectionPool.totalCount || 0) - (connectionPool.freeCount || 0),
                utilizationRate: connectionPool.totalCount > 0
                    ? (((connectionPool.totalCount - connectionPool.freeCount) / connectionPool.totalCount) * 100).toFixed(2) + '%'
                    : '0%',
            };
        }
        return {
            connectionPool: poolStats,
            isConnected: this.dataSource.isInitialized,
            databaseName: this.dataSource.options.database,
            driverType: this.dataSource.options.type,
        };
    }
    async getCacheMetrics() {
        return {
            message: 'Cache metrics not yet implemented',
            timestamp: new Date().toISOString(),
        };
    }
    async getSystemMetrics() {
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        return {
            memory: {
                rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
                heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
                heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
                external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
                arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) + ' MB',
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system,
            },
            uptime: Math.round(process.uptime()) + ' seconds',
            nodeVersion: process.version,
            platform: process.platform,
            architecture: process.arch,
        };
    }
    async getHealthSummary() {
        const [apiReport, dbMetrics, systemMetrics] = await Promise.all([
            this.apiOptimizationService.exportDetailedReport(),
            this.getDatabaseMetrics(),
            this.getSystemMetrics(),
        ]);
        const healthScore = this.calculateHealthScore(apiReport, dbMetrics, systemMetrics);
        const status = healthScore >= 80 ? 'healthy' : healthScore >= 60 ? 'warning' : 'critical';
        return {
            status,
            healthScore,
            summary: {
                api: {
                    totalEndpoints: apiReport.summary.totalEndpoints,
                    averageResponseTime: Math.round(apiReport.summary.averageResponseTime),
                    errorRate: apiReport.summary.overallErrorRate.toFixed(2) + '%',
                    cacheHitRate: apiReport.summary.overallCacheHitRate.toFixed(2) + '%',
                },
                database: {
                    connected: dbMetrics.isConnected,
                    poolUtilization: dbMetrics.connectionPool?.utilizationRate || 'N/A',
                },
                system: {
                    memoryUsage: systemMetrics.memory.heapUsed,
                    uptime: systemMetrics.uptime,
                },
            },
            recommendations: [
                ...apiReport.suggestions,
                ...this.generateSystemRecommendations(systemMetrics, dbMetrics),
            ],
            timestamp: new Date().toISOString(),
        };
    }
    calculateHealthScore(apiReport, dbMetrics, systemMetrics) {
        let score = 100;
        if (apiReport.summary.averageResponseTime > 2000)
            score -= 20;
        else if (apiReport.summary.averageResponseTime > 1000)
            score -= 10;
        if (apiReport.summary.overallErrorRate > 10)
            score -= 20;
        else if (apiReport.summary.overallErrorRate > 5)
            score -= 10;
        if (!dbMetrics.isConnected)
            score -= 30;
        else if (dbMetrics.connectionPool) {
            const utilization = parseFloat(dbMetrics.connectionPool.utilizationRate);
            if (utilization > 90)
                score -= 15;
            else if (utilization > 80)
                score -= 10;
        }
        const heapUsed = parseFloat(systemMetrics.memory.heapUsed);
        const heapTotal = parseFloat(systemMetrics.memory.heapTotal);
        const memoryUtilization = (heapUsed / heapTotal) * 100;
        if (memoryUtilization > 90)
            score -= 15;
        else if (memoryUtilization > 80)
            score -= 10;
        return Math.max(0, score);
    }
    generateSystemRecommendations(systemMetrics, dbMetrics) {
        const recommendations = [];
        const heapUsed = parseFloat(systemMetrics.memory.heapUsed);
        const heapTotal = parseFloat(systemMetrics.memory.heapTotal);
        const memoryUtilization = (heapUsed / heapTotal) * 100;
        if (memoryUtilization > 80) {
            recommendations.push('内存使用率过高，建议优化内存使用或增加服务器内存');
        }
        if (dbMetrics.connectionPool) {
            const utilization = parseFloat(dbMetrics.connectionPool.utilizationRate);
            if (utilization > 80) {
                recommendations.push('数据库连接池使用率高，建议优化查询或增加连接池大小');
            }
        }
        const uptime = parseInt(systemMetrics.uptime);
        if (uptime > 30 * 24 * 3600) {
            recommendations.push('服务运行时间较长，建议定期重启以清理内存碎片');
        }
        return recommendations;
    }
};
exports.PerformanceController = PerformanceController;
__decorate([
    (0, common_1.Get)('api-metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get API performance metrics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'API metrics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PerformanceController.prototype, "getApiMetrics", null);
__decorate([
    (0, common_1.Get)('database-metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get database performance metrics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Database metrics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PerformanceController.prototype, "getDatabaseMetrics", null);
__decorate([
    (0, common_1.Get)('cache-metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get cache performance metrics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cache metrics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PerformanceController.prototype, "getCacheMetrics", null);
__decorate([
    (0, common_1.Get)('system-metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get system performance metrics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System metrics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PerformanceController.prototype, "getSystemMetrics", null);
__decorate([
    (0, common_1.Get)('health-summary'),
    (0, swagger_1.ApiOperation)({ summary: 'Get overall system health summary' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Health summary retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PerformanceController.prototype, "getHealthSummary", null);
exports.PerformanceController = PerformanceController = __decorate([
    (0, swagger_1.ApiTags)('Admin - Performance'),
    (0, common_1.Controller)('admin/performance'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [api_optimization_service_1.ApiOptimizationService,
        cache_service_1.CacheService,
        typeorm_1.DataSource])
], PerformanceController);
//# sourceMappingURL=performance.controller.js.map