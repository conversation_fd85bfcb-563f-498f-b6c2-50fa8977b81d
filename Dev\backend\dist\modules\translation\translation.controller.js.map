{"version": 3, "file": "translation.controller.js", "sourceRoot": "", "sources": ["../../../src/modules/translation/translation.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwH;AACxH,6CAAqE;AACrE,+DAA2D;AAC3D,kEAA6D;AAC7D,+BAA0D;AAE1D,uFAAkG;AAClG,2EAAiF;AACjF,mGAA8F;AAMvF,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IACH;IAA7B,YAA6B,kBAAsC;QAAtC,uBAAkB,GAAlB,kBAAkB,CAAoB;IAAG,CAAC;IAMjE,AAAN,KAAK,CAAC,SAAS,CAAS,YAA0B,EAAa,GAAyB;QACtF,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACtE,CAAC;IAOK,AAAN,KAAK,CAAC,UAAU,CAAY,GAAyB,EAAW,KAA0B;QACxF,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAOK,AAAN,KAAK,CAAC,QAAQ,CAAY,GAAyB;QACjD,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAMK,AAAN,KAAK,CAAC,cAAc,CAAc,EAAU,EAAa,GAAyB;QAChF,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjE,CAAC;IAMK,AAAN,KAAK,CAAC,iBAAiB,CAAc,EAAU,EAAa,GAAyB;QACnF,OAAO,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpE,CAAC;CACF,CAAA;AA5CY,sDAAqB;AAO1B;IAJL,IAAA,aAAI,GAAE;IACN,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAC3C,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,oCAAoC,EAAE,CAAC;IAC/E,IAAA,4BAAS,EAAC,EAAE,EAAE,KAAK,CAAC;IACJ,WAAA,IAAA,aAAI,GAAE,CAAA;IAA8B,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAAxB,kBAAY;;sDAEjD;AAOK;IALL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;IACpD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,4CAA4C,EAAE,CAAC;IACvF,IAAA,mCAAa,EAAC,GAAG,CAAC;IAClB,IAAA,4BAAS,EAAC,EAAE,EAAE,KAAK,CAAC;IACH,WAAA,IAAA,gBAAO,GAAE,CAAA;IAA6B,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAQ,yBAAmB;;uDAEzF;AAOK;IALL,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,+CAA+C,EAAE,CAAC;IAC1F,IAAA,mCAAa,EAAC,GAAG,CAAC;IAClB,IAAA,4BAAS,EAAC,EAAE,EAAE,KAAK,CAAC;IACL,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;qDAExB;AAMK;IAJL,IAAA,aAAI,EAAC,cAAc,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,sCAAsC,EAAE,CAAC;IACjF,IAAA,4BAAS,EAAC,GAAG,EAAE,KAAK,CAAC;IACA,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;2DAEvD;AAMK;IAJL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;IACtD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,kCAAkC,EAAE,CAAC;IAC7E,IAAA,4BAAS,EAAC,EAAE,EAAE,KAAK,CAAC;IACI,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IAAc,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;8DAE1D;gCA3CU,qBAAqB;IAJjC,IAAA,iBAAO,EAAC,aAAa,CAAC;IACtB,IAAA,mBAAU,EAAC,aAAa,CAAC;IACzB,IAAA,kBAAS,EAAC,6BAAY,EAAE,iCAAc,CAAC;IACvC,IAAA,wBAAe,EAAC,mDAAuB,EAAE,wCAAkB,CAAC;qCAEV,wCAAkB;GADxD,qBAAqB,CA4CjC"}