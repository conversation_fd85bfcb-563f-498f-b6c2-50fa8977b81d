{"version": 3, "file": "translation-cache.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/translation/services/translation-cache.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,0EAAsE;AAI/D,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAaL;IAZZ,qBAAqB,GAAG,aAAa,CAAC;IACtC,eAAe,GAAG,OAAO,CAAC;IAC1B,iBAAiB,GAAG,SAAS,CAAC;IAG9B,SAAS,GAAG;QAC3B,WAAW,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QACzB,KAAK,EAAE,EAAE,GAAG,EAAE;QACd,OAAO,EAAE,EAAE,GAAG,EAAE;QAChB,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;KACjC,CAAC;IAEF,YAA6B,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;IAAG,CAAC;IAKnD,sBAAsB,CAAC,UAAkB,EAAE,UAAkB,EAAE,UAAkB;QAEvF,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC,CAAC;QACnE,OAAO,GAAG,UAAU,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;IAC/C,CAAC;IAKO,UAAU,CAAC,GAAW;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,UAAkB,EAClB,UAAkB,EAClB,UAAkB,EAClB,MAAyB;QAEzB,MAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAE5E,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE;YACvC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;YAC/B,SAAS,EAAE,IAAI,CAAC,qBAAqB;SACtC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,UAAkB,EAClB,UAAkB,EAClB,UAAkB;QAElB,MAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAE5E,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAoB,GAAG,EAAE;YACzD,SAAS,EAAE,IAAI,CAAC,qBAAqB;SACtC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,KAAU;QAC7C,MAAM,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;QAE7B,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE;YACtC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;YACzB,SAAS,EAAE,IAAI,CAAC,eAAe;SAChC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,MAAM,GAAG,GAAG,QAAQ,MAAM,EAAE,CAAC;QAE7B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE;YACtC,SAAS,EAAE,IAAI,CAAC,eAAe;SAChC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,MAAc,EACd,IAAY,EACZ,KAAa,EACb,OAAY,EACZ,OAAY;QAEZ,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,QAAQ,MAAM,KAAK,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,CAAC;QAE7D,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE;YACxC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;YAC3B,SAAS,EAAE,IAAI,CAAC,iBAAiB;SAClC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,MAAc,EACd,IAAY,EACZ,KAAa,EACb,OAAY;QAEZ,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,QAAQ,MAAM,KAAK,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,CAAC;QAE7D,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE;YACtC,SAAS,EAAE,IAAI,CAAC,iBAAiB;SAClC,CAAC,CAAC;IACL,CAAC;IAKO,iBAAiB,CAAC,OAAY;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc;QAEjC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,MAAM,EAAE,EAAE;YAC5C,SAAS,EAAE,IAAI,CAAC,eAAe;SAChC,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,MAAM,IAAI,EAAE;YACrD,SAAS,EAAE,IAAI,CAAC,iBAAiB;SAClC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,qBAAqB;QACzB,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAC5D,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,KAAY;QAC1C,MAAM,GAAG,GAAG,eAAe,CAAC;QAE5B,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE;YACtC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc;YAClC,SAAS,EAAE,IAAI,CAAC,eAAe;SAChC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,6BAA6B;QACjC,MAAM,GAAG,GAAG,eAAe,CAAC;QAE5B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE;YACtC,SAAS,EAAE,IAAI,CAAC,eAAe;SAChC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,YAKxB;QACA,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3C,GAAG,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC;YACnF,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;SAChC,CAAC,CAAC,CAAC;QAEJ,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE;YACvC,SAAS,EAAE,IAAI,CAAC,qBAAqB;SACtC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,aAAa;QAKjB,IAAI,CAAC;YAEH,MAAM,CAAC,eAAe,EAAE,SAAS,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC;gBACpD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC9C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC;aACjD,CAAC,CAAC;YAEH,OAAO;gBACL,oBAAoB,EAAE,eAAyB;gBAC/C,cAAc,EAAE,SAAmB;gBACnC,gBAAgB,EAAE,WAAqB;aACxC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,oBAAoB,EAAE,CAAC;gBACvB,cAAc,EAAE,CAAC;gBACjB,gBAAgB,EAAE,CAAC;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;IAGlB,CAAC;CACF,CAAA;AA9OY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAcgC,4BAAY;GAb5C,uBAAuB,CA8OnC"}