import { CacheService } from '../../../common/services/cache.service';
import { TranslationResult } from '../interfaces';
export declare class TranslationCacheService {
    private readonly cacheService;
    private readonly TRANSLATION_NAMESPACE;
    private readonly STATS_NAMESPACE;
    private readonly HISTORY_NAMESPACE;
    private readonly CACHE_TTL;
    constructor(cacheService: CacheService);
    private generateTranslationKey;
    private simpleHash;
    cacheTranslation(sourceText: string, sourceLang: string, targetLang: string, result: TranslationResult): Promise<void>;
    getCachedTranslation(sourceText: string, sourceLang: string, targetLang: string): Promise<TranslationResult | null>;
    cacheUserStats(userId: string, stats: any): Promise<void>;
    getCachedUserStats(userId: string): Promise<any | null>;
    cacheUserHistory(userId: string, page: number, limit: number, filters: any, history: any): Promise<void>;
    getCachedUserHistory(userId: string, page: number, limit: number, filters: any): Promise<any | null>;
    private generateFilterKey;
    clearUserCache(userId: string): Promise<void>;
    clearTranslationCache(): Promise<void>;
    cachePopularLanguagePairs(pairs: any[]): Promise<void>;
    getCachedPopularLanguagePairs(): Promise<any[] | null>;
    preloadTranslations(translations: Array<{
        sourceText: string;
        sourceLang: string;
        targetLang: string;
        result: TranslationResult;
    }>): Promise<void>;
    getCacheStats(): Promise<{
        translationCacheSize: number;
        statsCacheSize: number;
        historyCacheSize: number;
    }>;
    smartCleanup(): Promise<void>;
}
