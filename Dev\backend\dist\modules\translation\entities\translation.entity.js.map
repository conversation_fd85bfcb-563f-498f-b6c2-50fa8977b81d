{"version": 3, "file": "translation.entity.js", "sourceRoot": "", "sources": ["../../../../src/modules/translation/entities/translation.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAmI;AACnI,iEAAuD;AAQhD,IAAM,WAAW,GAAjB,MAAM,WAAW;IAEtB,EAAE,CAAS;IAIX,MAAM,CAAS;IAIf,IAAI,CAAO;IAGX,UAAU,CAAS;IAGnB,cAAc,CAAS;IAGvB,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,MAAM,CAAS;IAGf,UAAU,CAAS;IAGnB,OAAO,CAAS;IAGhB,UAAU,CAAU;IAGpB,MAAM,CAAU;IAGhB,QAAQ,CAAS;IAGjB,QAAQ,CAAM;IAGd,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AAlDY,kCAAW;AAEtB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;uCACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC1B,IAAA,eAAK,GAAE;;2CACO;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,CAAC;IACrB,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;8BACzB,kBAAI;yCAAC;AAGX;IADC,IAAA,gBAAM,EAAC,MAAM,CAAC;;+CACI;AAGnB;IADC,IAAA,gBAAM,EAAC,MAAM,CAAC;;mDACQ;AAGvB;IADC,IAAA,gBAAM,GAAE;;+CACU;AAGnB;IADC,IAAA,gBAAM,GAAE;;+CACU;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;2CACxF;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACjD;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACX;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;+CACP;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;2CACX;AAGhB;IADC,IAAA,gBAAM,EAAC,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CACb;AAGjB;IADC,IAAA,gBAAM,EAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CACtB;AAGd;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;8CAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;8CAAC;sBAjDL,WAAW;IANvB,IAAA,gBAAM,EAAC,cAAc,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC9B,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IAC7C,IAAA,eAAK,EAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;GACL,WAAW,CAkDvB"}