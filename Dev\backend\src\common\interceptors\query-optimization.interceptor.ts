import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { DataSource } from 'typeorm';

@Injectable()
export class QueryOptimizationInterceptor implements NestInterceptor {
  private readonly logger = new Logger(QueryOptimizationInterceptor.name);

  constructor(private readonly dataSource: DataSource) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();

    // 记录活跃连接数
    const connectionPool = (this.dataSource.driver as any).pool;
    
    if (connectionPool) {
      const activeConnections = connectionPool.totalCount;
      const freeConnections = connectionPool.freeCount;
      
      if (activeConnections && freeConnections !== undefined) {
        const utilizationRate = ((activeConnections - freeConnections) / activeConnections) * 100;
        
        // 记录连接池状态
        request.dbPoolInfo = {
          active: activeConnections,
          free: freeConnections,
          utilization: utilizationRate.toFixed(2) + '%',
        };

        // 警告高连接池使用率
        if (utilizationRate > 80) {
          this.logger.warn(
            `High database connection pool utilization: ${utilizationRate.toFixed(2)}%`,
            {
              active: activeConnections,
              free: freeConnections,
              url: request.originalUrl,
            }
          );
        }
      }
    }

    return next.handle().pipe(
      tap(() => {
        const queryTime = Date.now() - startTime;
        
        // 记录慢查询
        if (queryTime > 500) {
          this.logger.warn(
            `Slow query detected: ${request.method} ${request.originalUrl} - ${queryTime}ms`,
            {
              method: request.method,
              url: request.originalUrl,
              queryTime,
              dbPoolInfo: request.dbPoolInfo,
            }
          );
        }

        // 在开发环境记录查询信息
        if (process.env.NODE_ENV === 'development' && request.dbPoolInfo) {
          this.logger.debug(
            `DB Pool: ${request.dbPoolInfo.utilization} utilization, Query: ${queryTime}ms`
          );
        }
      })
    );
  }
}