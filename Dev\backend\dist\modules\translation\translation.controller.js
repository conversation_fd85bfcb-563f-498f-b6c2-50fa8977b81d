"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const translation_service_1 = require("./translation.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const dto_1 = require("./dto");
const caching_interceptor_1 = require("../../common/interceptors/caching.interceptor");
const rate_limit_guard_1 = require("../../common/guards/rate-limit.guard");
const response_time_interceptor_1 = require("../../common/interceptors/response-time.interceptor");
let TranslationController = class TranslationController {
    translationService;
    constructor(translationService) {
        this.translationService = translationService;
    }
    async translate(translateDto, req) {
        return this.translationService.translate(translateDto, req.user.id);
    }
    async getHistory(req, query) {
        return this.translationService.getHistory(req.user.id, query);
    }
    async getStats(req) {
        return this.translationService.getStats(req.user.id);
    }
    async toggleFavorite(id, req) {
        return this.translationService.toggleFavorite(id, req.user.id);
    }
    async deleteTranslation(id, req) {
        return this.translationService.deleteTranslation(id, req.user.id);
    }
};
exports.TranslationController = TranslationController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Translate text' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Translation completed successfully' }),
    (0, rate_limit_guard_1.RateLimit)(30, 60000),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.TranslateDto, Object]),
    __metadata("design:returntype", Promise)
], TranslationController.prototype, "translate", null);
__decorate([
    (0, common_1.Get)('history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get translation history' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Translation history retrieved successfully' }),
    (0, caching_interceptor_1.CacheResponse)(180),
    (0, rate_limit_guard_1.RateLimit)(60, 60000),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.TranslationQueryDto]),
    __metadata("design:returntype", Promise)
], TranslationController.prototype, "getHistory", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get translation statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Translation statistics retrieved successfully' }),
    (0, caching_interceptor_1.CacheResponse)(300),
    (0, rate_limit_guard_1.RateLimit)(30, 60000),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TranslationController.prototype, "getStats", null);
__decorate([
    (0, common_1.Post)(':id/favorite'),
    (0, swagger_1.ApiOperation)({ summary: 'Toggle translation favorite status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Favorite status updated successfully' }),
    (0, rate_limit_guard_1.RateLimit)(100, 60000),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TranslationController.prototype, "toggleFavorite", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete translation record' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Translation deleted successfully' }),
    (0, rate_limit_guard_1.RateLimit)(20, 60000),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TranslationController.prototype, "deleteTranslation", null);
exports.TranslationController = TranslationController = __decorate([
    (0, swagger_1.ApiTags)('Translation'),
    (0, common_1.Controller)('translation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, rate_limit_guard_1.RateLimitGuard),
    (0, common_1.UseInterceptors)(response_time_interceptor_1.ResponseTimeInterceptor, caching_interceptor_1.CachingInterceptor),
    __metadata("design:paramtypes", [translation_service_1.TranslationService])
], TranslationController);
//# sourceMappingURL=translation.controller.js.map